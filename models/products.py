"""
Product data models and management.
"""
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class Product:
    """Product data model."""
    name: str
    price: int
    documents: List[str]
    available_slots: int
    time_slots: List[str]
    category: str = "course"
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert product to dictionary."""
        return {
            "name": self.name,
            "price": self.price,
            "documents": self.documents,
            "available_slots": self.available_slots,
            "time_slots": self.time_slots,
            "category": self.category,
            "description": self.description
        }


class ProductDatabase:
    """Product database management."""
    
    def __init__(self):
        self.products = self._load_products()
    
    def _load_products(self) -> List[Product]:
        """Load products from data."""
        products_data = [
            {
                "name": "SEE",
                "price": 1499,
                "documents": ["Passport-size photo", "School ID", "Grade 9 Marksheet"],
                "available_slots": 25,
                "time_slots": ["6:00 AM - 8:00 AM", "4:00 PM - 6:00 PM"],
                "category": "secondary_education",
                "description": "Secondary Education Examination preparation course"
            },
            {
                "name": "Bridge Course",
                "price": 1999,
                "documents": ["SEE Certificate", "Passport-size photo"],
                "available_slots": 30,
                "time_slots": ["7:00 AM - 9:00 AM", "3:00 PM - 5:00 PM"],
                "category": "bridge_program",
                "description": "Bridge course for higher secondary preparation"
            },
            {
                "name": "BBS",
                "price": 2499,
                "documents": ["+2 Transcript", "Citizenship Copy"],
                "available_slots": 20,
                "time_slots": ["5:00 PM - 7:00 PM"],
                "category": "bachelor_degree",
                "description": "Bachelor of Business Studies program"
            },
            {
                "name": "BBS (Finance)",
                "price": 2699,
                "documents": ["+2 Transcript", "Citizenship Copy", "Bank Recommendation Letter"],
                "available_slots": 15,
                "time_slots": ["6:00 AM - 8:00 AM"],
                "category": "bachelor_degree",
                "description": "Bachelor of Business Studies with Finance specialization"
            },
            {
                "name": "CSIT",
                "price": 2999,
                "documents": ["+2 Transcript", "CSIT Entrance Card", "Citizenship Copy"],
                "available_slots": 25,
                "time_slots": ["8:00 AM - 10:00 AM"],
                "category": "bachelor_degree",
                "description": "Computer Science and Information Technology program"
            },
            {
                "name": "IELTS",
                "price": 4999,
                "documents": ["Passport", "Passport-size photo"],
                "available_slots": 10,
                "time_slots": ["7:00 AM - 9:00 AM", "5:00 PM - 7:00 PM"],
                "category": "language_test",
                "description": "International English Language Testing System preparation"
            },
            {
                "name": "GRE",
                "price": 6999,
                "documents": ["Passport", "Academic Transcript"],
                "available_slots": 10,
                "time_slots": ["6:00 AM - 8:00 AM"],
                "category": "standardized_test",
                "description": "Graduate Record Examination preparation"
            },
            {
                "name": "TOEFL",
                "price": 4599,
                "documents": ["Passport", "Passport-size photo"],
                "available_slots": 12,
                "time_slots": ["4:00 PM - 6:00 PM"],
                "category": "language_test",
                "description": "Test of English as a Foreign Language preparation"
            },
            {
                "name": "SAT",
                "price": 5999,
                "documents": ["Passport", "High School Transcript"],
                "available_slots": 15,
                "time_slots": ["6:00 AM - 8:00 AM", "6:00 PM - 8:00 PM"],
                "category": "standardized_test",
                "description": "Scholastic Assessment Test preparation"
            },
            {
                "name": "GMAT",
                "price": 7499,
                "documents": ["Passport", "Bachelor Transcript"],
                "available_slots": 8,
                "time_slots": ["7:00 PM - 9:00 PM"],
                "category": "standardized_test",
                "description": "Graduate Management Admission Test preparation"
            }
        ]
        
        return [Product(**data) for data in products_data]
    
    def get_all_products(self) -> List[Product]:
        """Get all products."""
        return self.products
    
    def get_product_by_name(self, name: str) -> Optional[Product]:
        """Get product by name."""
        for product in self.products:
            if product.name.lower() == name.lower():
                return product
        return None
    
    def get_products_by_category(self, category: str) -> List[Product]:
        """Get products by category."""
        return [p for p in self.products if p.category == category]
    
    def get_products_by_price_range(self, min_price: int, max_price: int) -> List[Product]:
        """Get products within price range."""
        return [p for p in self.products if min_price <= p.price <= max_price]
