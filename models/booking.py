"""
Booking data models and management.
"""
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import uuid


class BookingStatus(Enum):
    """Booking status enumeration."""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"


@dataclass
class Booking:
    """Booking data model."""
    booking_id: str
    product_name: str
    customer_name: str
    customer_email: str
    customer_phone: str
    time_slot: str
    booking_date: datetime
    status: BookingStatus
    created_at: datetime
    notes: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert booking to dictionary."""
        return {
            "booking_id": self.booking_id,
            "product_name": self.product_name,
            "customer_name": self.customer_name,
            "customer_email": self.customer_email,
            "customer_phone": self.customer_phone,
            "time_slot": self.time_slot,
            "booking_date": self.booking_date.isoformat(),
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "notes": self.notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Booking':
        """Create booking from dictionary."""
        return cls(
            booking_id=data["booking_id"],
            product_name=data["product_name"],
            customer_name=data["customer_name"],
            customer_email=data["customer_email"],
            customer_phone=data["customer_phone"],
            time_slot=data["time_slot"],
            booking_date=datetime.fromisoformat(data["booking_date"]),
            status=BookingStatus(data["status"]),
            created_at=datetime.fromisoformat(data["created_at"]),
            notes=data.get("notes", "")
        )
    
    def generate_booking_id(self) -> str:
        """Generate a unique booking ID."""
        return f"BK-{uuid.uuid4().hex[:8].upper()}"


class BookingDatabase:
    """In-memory booking database."""
    
    def __init__(self):
        self.bookings: Dict[str, Booking] = {}
    
    def create_booking(self, product_name: str, customer_name: str, 
                      customer_email: str, customer_phone: str, 
                      time_slot: str, booking_date: datetime, 
                      notes: str = "") -> Booking:
        """Create a new booking."""
        booking_id = f"BK-{uuid.uuid4().hex[:8].upper()}"
        
        booking = Booking(
            booking_id=booking_id,
            product_name=product_name,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_phone=customer_phone,
            time_slot=time_slot,
            booking_date=booking_date,
            status=BookingStatus.PENDING,
            created_at=datetime.now(),
            notes=notes
        )
        
        self.bookings[booking_id] = booking
        return booking
    
    def get_booking(self, booking_id: str) -> Optional[Booking]:
        """Get booking by ID."""
        return self.bookings.get(booking_id)
    
    def update_booking_status(self, booking_id: str, status: BookingStatus) -> bool:
        """Update booking status."""
        if booking_id in self.bookings:
            self.bookings[booking_id].status = status
            return True
        return False
    
    def get_bookings_by_customer(self, customer_email: str) -> list[Booking]:
        """Get all bookings for a customer."""
        return [booking for booking in self.bookings.values() 
                if booking.customer_email == customer_email]
    
    def get_confirmed_bookings(self) -> list[Booking]:
        """Get all confirmed bookings."""
        return [booking for booking in self.bookings.values() 
                if booking.status == BookingStatus.CONFIRMED]
