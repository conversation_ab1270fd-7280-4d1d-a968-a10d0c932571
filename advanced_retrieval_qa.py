"""
Advanced Retrieval QA system with token cost tracking and best practices.
"""
import os
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# LangChain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore
from langchain_core.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableParallel
from langchain_core.documents import Document
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.messages import BaseMessage

# Qdrant imports
from qdrant_client import QdrantClient
from dotenv import load_dotenv

load_dotenv()


@dataclass
class TokenUsage:
    """Track token usage and costs."""
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    cost: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class TokenTrackingCallback(BaseCallbackHandler):
    """Callback handler to track token usage and costs."""
    
    def __init__(self):
        self.token_usage = []
        self.current_run_tokens = TokenUsage()
        
        # Pricing per 1K tokens (update these based on current pricing)
        self.pricing = {
            "gemini-1.5-flash": {
                "input": 0.000075,   # $0.075 per 1K input tokens
                "output": 0.0003     # $0.30 per 1K output tokens
            },
            "text-embedding-3-large": {
                "input": 0.00013,    # $0.13 per 1K tokens
                "output": 0.0        # No output cost for embeddings
            }
        }
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """Called when LLM starts running."""
        self.current_run_tokens = TokenUsage()
    
    def on_llm_end(self, response, **kwargs) -> None:
        """Called when LLM ends running."""
        if hasattr(response, 'llm_output') and response.llm_output:
            token_usage = response.llm_output.get('token_usage', {})
            
            prompt_tokens = token_usage.get('prompt_tokens', 0)
            completion_tokens = token_usage.get('completion_tokens', 0)
            total_tokens = token_usage.get('total_tokens', prompt_tokens + completion_tokens)
            
            # Calculate cost (assuming Gemini pricing)
            model_name = "gemini-1.5-flash"
            pricing = self.pricing.get(model_name, {"input": 0, "output": 0})
            
            input_cost = (prompt_tokens / 1000) * pricing["input"]
            output_cost = (completion_tokens / 1000) * pricing["output"]
            total_cost = input_cost + output_cost
            
            usage = TokenUsage(
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=total_cost
            )
            
            self.token_usage.append(usage)
    
    def get_total_cost(self) -> float:
        """Get total cost across all runs."""
        return sum(usage.cost for usage in self.token_usage)
    
    def get_total_tokens(self) -> int:
        """Get total tokens across all runs."""
        return sum(usage.total_tokens for usage in self.token_usage)
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get comprehensive usage summary."""
        if not self.token_usage:
            return {"total_cost": 0, "total_tokens": 0, "runs": 0}
        
        return {
            "total_cost": self.get_total_cost(),
            "total_tokens": self.get_total_tokens(),
            "total_prompt_tokens": sum(u.prompt_tokens for u in self.token_usage),
            "total_completion_tokens": sum(u.completion_tokens for u in self.token_usage),
            "runs": len(self.token_usage),
            "average_cost_per_run": self.get_total_cost() / len(self.token_usage),
            "runs_detail": [
                {
                    "timestamp": usage.timestamp.isoformat(),
                    "prompt_tokens": usage.prompt_tokens,
                    "completion_tokens": usage.completion_tokens,
                    "total_tokens": usage.total_tokens,
                    "cost": usage.cost
                }
                for usage in self.token_usage
            ]
        }


class AdvancedRetrievalQA:
    """Advanced Retrieval QA system with token tracking and optimization."""
    
    def __init__(self, 
                 collection_name: str = "langsmit_test",
                 qdrant_host: str = "*************",
                 qdrant_port: int = 6333,
                 model_name: str = "gemini-1.5-flash",
                 temperature: float = 0.1,
                 max_tokens: int = 1000):
        
        self.collection_name = collection_name
        self.model_name = model_name
        self.token_callback = TokenTrackingCallback()
        
        # Initialize components
        self._setup_llm(model_name, temperature, max_tokens)
        self._setup_embeddings()
        self._setup_vector_store(qdrant_host, qdrant_port)
        self._setup_retrieval_chain()
    
    def _setup_llm(self, model_name: str, temperature: float, max_tokens: int):
        """Setup the language model with token tracking."""
        self.llm = ChatGoogleGenerativeAI(
            model=model_name,
            temperature=temperature,
            max_output_tokens=max_tokens,
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            callbacks=[self.token_callback]
        )
    
    def _setup_embeddings(self):
        """Setup embeddings model."""
        self.embeddings = OpenAIEmbeddings(
            model="text-embedding-3-large",
            api_key=os.getenv("OPENAI_API_KEY"),
            dimensions=1536
        )
    
    def _setup_vector_store(self, host: str, port: int):
        """Setup Qdrant vector store."""
        self.qdrant_client = QdrantClient(host=host, port=port)
        
        self.vector_store = QdrantVectorStore(
            client=self.qdrant_client,
            collection_name=self.collection_name,
            embedding=self.embeddings,
            content_payload_key="page_content",
            metadata_payload_key="metadata"
        )
        
        # Create retriever with optimized settings
        self.retriever = self.vector_store.as_retriever(
            search_type="similarity",
            search_kwargs={
                "k": 5,  # Number of documents to retrieve
                "score_threshold": 0.7  # Minimum similarity score
            }
        )
    
    def _setup_retrieval_chain(self):
        """Setup the retrieval chain with optimized prompts."""
        
        # Enhanced system prompt for better QA
        system_prompt = """You are a helpful AI assistant that answers questions based on the provided context.

Instructions:
1. Use ONLY the information provided in the context to answer questions
2. If the context doesn't contain enough information, say "I don't have enough information to answer that question"
3. Be specific and cite relevant parts of the context when possible
4. Provide clear, concise, and accurate answers
5. If asked about sources, mention the source information from the context

Context: {context}

Question: {input}

Answer:"""
        
        # Create prompt template
        prompt = ChatPromptTemplate.from_template(system_prompt)
        
        # Create document chain
        document_chain = create_stuff_documents_chain(
            llm=self.llm,
            prompt=prompt
        )
        
        # Create retrieval chain
        self.retrieval_chain = create_retrieval_chain(
            retriever=self.retriever,
            combine_docs_chain=document_chain
        )
    
    def query(self, question: str, return_sources: bool = True) -> Dict[str, Any]:
        """
        Query the system with token tracking.
        
        Args:
            question: The question to ask
            return_sources: Whether to return source documents
            
        Returns:
            Dictionary with answer, sources, and token usage
        """
        start_time = time.time()
        
        # Track tokens before query
        tokens_before = len(self.token_callback.token_usage)
        
        try:
            # Run the retrieval chain
            result = self.retrieval_chain.invoke({"input": question})
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Get token usage for this query
            tokens_after = len(self.token_callback.token_usage)
            current_usage = None
            if tokens_after > tokens_before:
                current_usage = self.token_callback.token_usage[-1]
            
            # Format response
            response = {
                "question": question,
                "answer": result.get("answer", "No answer generated"),
                "processing_time": round(processing_time, 2),
                "token_usage": {
                    "prompt_tokens": current_usage.prompt_tokens if current_usage else 0,
                    "completion_tokens": current_usage.completion_tokens if current_usage else 0,
                    "total_tokens": current_usage.total_tokens if current_usage else 0,
                    "cost": current_usage.cost if current_usage else 0.0
                }
            }
            
            # Add sources if requested
            if return_sources and "context" in result:
                sources = []
                for doc in result["context"]:
                    source_info = {
                        "content": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                        "metadata": doc.metadata,
                        "source": doc.metadata.get("source", "Unknown")
                    }
                    sources.append(source_info)
                response["sources"] = sources
            
            return response
            
        except Exception as e:
            return {
                "question": question,
                "answer": f"Error: {str(e)}",
                "processing_time": round(time.time() - start_time, 2),
                "token_usage": {"error": "Failed to track tokens"},
                "sources": [] if return_sources else None
            }
    
    def batch_query(self, questions: List[str]) -> List[Dict[str, Any]]:
        """Process multiple questions efficiently."""
        results = []
        for question in questions:
            result = self.query(question)
            results.append(result)
        return results
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get comprehensive usage and cost summary."""
        return self.token_callback.get_usage_summary()
    
    def reset_usage_tracking(self):
        """Reset token usage tracking."""
        self.token_callback = TokenTrackingCallback()
        self.llm.callbacks = [self.token_callback]


# Example usage and testing
def main():
    """Example usage of the Advanced Retrieval QA system."""
    
    print("🚀 Initializing Advanced Retrieval QA System...")
    qa_system = AdvancedRetrievalQA()
    
    # Test questions
    test_questions = [
        "What is Ambition Guru?",
        "How can I download the app?",
        "What courses are available?",
        "What are the requirements for IELTS?",
        "How much does the CSIT course cost?"
    ]
    
    print("\n📝 Running test queries...")
    for i, question in enumerate(test_questions, 1):
        print(f"\n--- Query {i} ---")
        print(f"Question: {question}")
        
        result = qa_system.query(question)
        
        print(f"Answer: {result['answer']}")
        print(f"Processing time: {result['processing_time']}s")
        print(f"Tokens used: {result['token_usage']['total_tokens']}")
        print(f"Cost: ${result['token_usage']['cost']:.6f}")
        
        if result.get('sources'):
            print(f"Sources found: {len(result['sources'])}")
    
    # Print usage summary
    print("\n📊 Usage Summary:")
    summary = qa_system.get_usage_summary()
    print(f"Total cost: ${summary['total_cost']:.6f}")
    print(f"Total tokens: {summary['total_tokens']}")
    print(f"Total queries: {summary['runs']}")
    print(f"Average cost per query: ${summary['average_cost_per_run']:.6f}")


def create_legacy_qa_system():
    """Create QAWithSourcesChain for comparison (deprecated but functional)."""
    from langchain.chains.qa_with_sources import load_qa_with_sources_chain
    from langchain.chains import RetrievalQAWithSourcesChain

    # Setup components
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        temperature=0.1,
        google_api_key=os.getenv("GOOGLE_API_KEY")
    )

    embeddings = OpenAIEmbeddings(
        model="text-embedding-3-large",
        api_key=os.getenv("OPENAI_API_KEY"),
        dimensions=1536
    )

    qdrant_client = QdrantClient(host="*************", port=6333)
    vector_store = QdrantVectorStore(
        client=qdrant_client,
        collection_name="langsmit_test",
        embedding=embeddings,
        content_payload_key="page_content",
        metadata_payload_key="metadata"
    )

    # Create legacy chain
    qa_chain = RetrievalQAWithSourcesChain.from_chain_type(
        llm=llm,
        chain_type="stuff",
        retriever=vector_store.as_retriever(search_kwargs={"k": 5}),
        return_source_documents=True
    )

    return qa_chain


def compare_systems():
    """Compare modern vs legacy QA systems."""
    print("🔄 Comparing Modern vs Legacy QA Systems")
    print("=" * 60)

    # Initialize both systems
    print("Initializing systems...")
    modern_qa = AdvancedRetrievalQA()

    try:
        legacy_qa = create_legacy_qa_system()
        legacy_available = True
    except Exception as e:
        print(f"⚠️ Legacy system unavailable: {e}")
        legacy_available = False

    test_question = "What is Ambition Guru and how can I download the app?"

    # Test modern system
    print(f"\n🆕 Modern System Results:")
    print("-" * 30)
    start_time = time.time()
    modern_result = modern_qa.query(test_question)
    modern_time = time.time() - start_time

    print(f"Answer: {modern_result['answer'][:200]}...")
    print(f"Processing time: {modern_time:.2f}s")
    print(f"Token usage: {modern_result['token_usage']}")
    print(f"Sources: {len(modern_result.get('sources', []))}")

    # Test legacy system if available
    if legacy_available:
        print(f"\n🔄 Legacy System Results:")
        print("-" * 30)
        start_time = time.time()
        try:
            legacy_result = legacy_qa({"question": test_question})
            legacy_time = time.time() - start_time

            print(f"Answer: {legacy_result.get('answer', 'No answer')[:200]}...")
            print(f"Processing time: {legacy_time:.2f}s")
            print(f"Sources: {legacy_result.get('sources', 'No sources')}")
            print("⚠️ No token tracking available in legacy system")
        except Exception as e:
            print(f"❌ Legacy system error: {e}")

    # Show advantages
    print(f"\n✅ Modern System Advantages:")
    print("- Comprehensive token cost tracking")
    print("- Better error handling and response formatting")
    print("- Optimized prompts and retrieval settings")
    print("- Batch processing capabilities")
    print("- Detailed usage analytics")
    print("- Modern LangChain patterns (not deprecated)")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "compare":
        compare_systems()
    else:
        main()
