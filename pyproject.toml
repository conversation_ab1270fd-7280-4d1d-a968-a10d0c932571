[project]
name = "langraph-test"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langchain>=0.3.26",
    "langchain-community>=0.3.26",
    "langchain-openai>=0.3.27",
    "langchain-qdrant>=0.2.0",
    "langgraph>=0.5.0",
    "langsmith>=0.4.4",
    "pandas>=2.3.0",
    "python-dotenv>=1.1.1",
    "qdrant-client>=1.14.3",
    "tavily-python>=0.7.8",
]

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
]
