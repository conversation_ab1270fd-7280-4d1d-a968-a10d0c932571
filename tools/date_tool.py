"""
Date utility tool for booking system.
"""
from datetime import datetime, date, timedelta
from typing import Dict, List, Any
import calendar


class DateTool:
    """Tool for date-related operations and utilities."""
    
    def get_current_date(self) -> Dict[str, Any]:
        """
        Get current date and time information.
        
        Returns:
            Dictionary with current date/time details
        """
        now = datetime.now()
        today = date.today()
        
        return {
            "current_datetime": now.isoformat(),
            "current_date": today.isoformat(),
            "current_time": now.strftime("%H:%M:%S"),
            "day_of_week": today.strftime("%A"),
            "month": today.strftime("%B"),
            "year": today.year,
            "formatted_date": today.strftime("%B %d, %Y"),
            "formatted_datetime": now.strftime("%B %d, %Y at %I:%M %p"),
            "timestamp": now.timestamp()
        }
    
    def get_date_info(self, date_string: str) -> Dict[str, Any]:
        """
        Get information about a specific date.
        
        Args:
            date_string: Date in YYYY-MM-DD format
            
        Returns:
            Dictionary with date information
        """
        try:
            target_date = datetime.fromisoformat(date_string).date()
            
            return {
                "date": target_date.isoformat(),
                "day_of_week": target_date.strftime("%A"),
                "month": target_date.strftime("%B"),
                "year": target_date.year,
                "formatted_date": target_date.strftime("%B %d, %Y"),
                "is_weekend": target_date.weekday() >= 5,
                "days_from_today": (target_date - date.today()).days
            }
        except ValueError:
            return {"error": "Invalid date format. Please use YYYY-MM-DD format."}
    
    def get_available_dates(self, days_ahead: int = 30) -> List[Dict[str, Any]]:
        """
        Get available booking dates for the next specified days.
        
        Args:
            days_ahead: Number of days to look ahead
            
        Returns:
            List of available dates (excluding weekends)
        """
        today = date.today()
        available_dates = []
        
        for i in range(1, days_ahead + 1):
            future_date = today + timedelta(days=i)
            
            # Skip weekends (Saturday=5, Sunday=6)
            if future_date.weekday() < 5:
                available_dates.append({
                    "date": future_date.isoformat(),
                    "day_of_week": future_date.strftime("%A"),
                    "formatted_date": future_date.strftime("%B %d, %Y"),
                    "days_from_today": i
                })
        
        return available_dates
    
    def is_valid_booking_date(self, date_string: str) -> Dict[str, Any]:
        """
        Check if a date is valid for booking.
        
        Args:
            date_string: Date in YYYY-MM-DD format
            
        Returns:
            Dictionary with validation result
        """
        try:
            target_date = datetime.fromisoformat(date_string).date()
            today = date.today()
            
            is_valid = True
            reasons = []
            
            # Check if date is in the past
            if target_date <= today:
                is_valid = False
                reasons.append("Date must be in the future")
            
            # Check if date is a weekend
            if target_date.weekday() >= 5:
                is_valid = False
                reasons.append("Bookings not available on weekends")
            
            # Check if date is too far in the future (e.g., more than 90 days)
            if (target_date - today).days > 90:
                is_valid = False
                reasons.append("Date is too far in the future (max 90 days)")
            
            return {
                "is_valid": is_valid,
                "date": target_date.isoformat(),
                "reasons": reasons if not is_valid else ["Date is valid for booking"]
            }
            
        except ValueError:
            return {
                "is_valid": False,
                "date": date_string,
                "reasons": ["Invalid date format. Please use YYYY-MM-DD format."]
            }
    
    def get_next_available_date(self) -> Dict[str, Any]:
        """
        Get the next available booking date.
        
        Returns:
            Dictionary with next available date
        """
        today = date.today()
        next_date = today + timedelta(days=1)
        
        # Find next weekday
        while next_date.weekday() >= 5:
            next_date += timedelta(days=1)
        
        return {
            "date": next_date.isoformat(),
            "day_of_week": next_date.strftime("%A"),
            "formatted_date": next_date.strftime("%B %d, %Y"),
            "days_from_today": (next_date - today).days
        }
    
    def format_booking_datetime(self, date_string: str, time_slot: str) -> Dict[str, Any]:
        """
        Format booking date and time for display.
        
        Args:
            date_string: Date in YYYY-MM-DD format
            time_slot: Time slot string
            
        Returns:
            Dictionary with formatted datetime information
        """
        try:
            booking_date = datetime.fromisoformat(date_string).date()
            
            return {
                "booking_date": booking_date.isoformat(),
                "time_slot": time_slot,
                "formatted_date": booking_date.strftime("%B %d, %Y"),
                "day_of_week": booking_date.strftime("%A"),
                "full_booking_info": f"{booking_date.strftime('%B %d, %Y')} ({booking_date.strftime('%A')}) at {time_slot}"
            }
            
        except ValueError:
            return {"error": "Invalid date format. Please use YYYY-MM-DD format."}
    
    def get_month_calendar(self, year: int = None, month: int = None) -> Dict[str, Any]:
        """
        Get calendar view for a specific month.
        
        Args:
            year: Year (defaults to current year)
            month: Month (defaults to current month)
            
        Returns:
            Dictionary with calendar information
        """
        if year is None:
            year = date.today().year
        if month is None:
            month = date.today().month
        
        cal = calendar.monthcalendar(year, month)
        month_name = calendar.month_name[month]
        
        return {
            "year": year,
            "month": month,
            "month_name": month_name,
            "calendar": cal,
            "formatted_title": f"{month_name} {year}"
        }
