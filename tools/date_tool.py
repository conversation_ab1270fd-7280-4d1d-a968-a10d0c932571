"""
Date tool to get current date.
"""
from datetime import datetime, date


def get_current_date() -> str:
    """
    Get current date information.

    Returns:
        Formatted string with current date details
    """
    now = datetime.now()
    today = date.today()

    return f"""📅 Current Date Information:

• Date: {today.strftime('%Y-%m-%d')}
• Day: {today.strftime('%A')}
• Month: {today.strftime('%B')}
• Year: {today.year}
• Time: {now.strftime('%H:%M:%S')}
• Formatted: {today.strftime('%B %d, %Y')}
• Full: {now.strftime('%B %d, %Y at %I:%M %p')}"""
