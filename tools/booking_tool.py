"""
Booking tool for appointment scheduling.
"""
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from models.products import ProductDatabase
from models.booking import BookingDatabase, BookingStatus


# Global instances
product_db = ProductDatabase()
booking_db = BookingDatabase()


def book_appointment(product_name: str, customer_name: str, customer_email: str, 
                    customer_phone: str, time_slot: str, booking_date: str) -> str:
    """
    Book an appointment for a product.
    
    Args:
        product_name: Name of the product to book
        customer_name: Customer's full name
        customer_email: Customer's email address
        customer_phone: Customer's phone number
        time_slot: Selected time slot
        booking_date: Date for booking (YYYY-MM-DD format)
        
    Returns:
        Booking confirmation message with booking ID
    """
    try:
        # Get product details
        product = product_db.get_product_by_name(product_name)
        if not product:
            return f"❌ Product '{product_name}' not found."
        
        # Check if product has available slots
        if product.available_slots <= 0:
            return f"❌ No available slots for '{product_name}'. All slots are booked."
        
        # Check if time slot is valid for this product
        if time_slot not in product.time_slots:
            available_slots = ", ".join(product.time_slots)
            return f"❌ Invalid time slot. Available slots for '{product_name}': {available_slots}"
        
        # Parse booking date
        try:
            booking_datetime = datetime.fromisoformat(booking_date)
        except ValueError:
            return "❌ Invalid date format. Please use YYYY-MM-DD format."
        
        # Create booking
        booking = booking_db.create_booking(
            product_name=product_name,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_phone=customer_phone,
            time_slot=time_slot,
            booking_date=booking_datetime
        )
        
        # Reduce available slots (simulate booking)
        product.available_slots -= 1
        
        return f"""✅ Booking Confirmed!
        
📋 Booking Details:
• Booking ID: {booking.booking_id}
• Product: {product_name}
• Customer: {customer_name}
• Email: {customer_email}
• Phone: {customer_phone}
• Date: {booking_date}
• Time: {time_slot}
• Status: {booking.status.value}

📝 Required Documents: {', '.join(product.documents)}
💰 Price: Rs. {product.price}
🎯 Remaining Slots: {product.available_slots}

Please save your Booking ID: {booking.booking_id}"""
        
    except Exception as e:
        return f"❌ Error creating booking: {str(e)}"


def get_booking_by_id(booking_id: str) -> str:
    """
    Get booking details by booking ID.
    
    Args:
        booking_id: The booking ID to search for
        
    Returns:
        Booking details or error message
    """
    try:
        booking = booking_db.get_booking(booking_id)
        if not booking:
            return f"❌ Booking with ID '{booking_id}' not found."
        
        return f"""📋 Booking Details:
        
• Booking ID: {booking.booking_id}
• Product: {booking.product_name}
• Customer: {booking.customer_name}
• Email: {booking.customer_email}
• Phone: {booking.customer_phone}
• Date: {booking.booking_date.strftime('%Y-%m-%d')}
• Time: {booking.time_slot}
• Status: {booking.status.value}
• Created: {booking.created_at.strftime('%Y-%m-%d %H:%M:%S')}
{f'• Notes: {booking.notes}' if booking.notes else ''}"""
        
    except Exception as e:
        return f"❌ Error retrieving booking: {str(e)}"


def confirm_booking(booking_id: str) -> str:
    """
    Confirm a booking by changing its status.
    
    Args:
        booking_id: The booking ID to confirm
        
    Returns:
        Confirmation message
    """
    try:
        booking = booking_db.get_booking(booking_id)
        if not booking:
            return f"❌ Booking with ID '{booking_id}' not found."
        
        if booking.status == BookingStatus.CONFIRMED:
            return f"ℹ️ Booking {booking_id} is already confirmed."
        
        # Update booking status
        success = booking_db.update_booking_status(booking_id, BookingStatus.CONFIRMED)
        
        if success:
            return f"✅ Booking {booking_id} has been confirmed!"
        else:
            return f"❌ Failed to confirm booking {booking_id}."
            
    except Exception as e:
        return f"❌ Error confirming booking: {str(e)}"


def get_my_bookings(customer_email: str) -> str:
    """
    Get all bookings for a customer.
    
    Args:
        customer_email: Customer's email address
        
    Returns:
        List of customer's bookings
    """
    try:
        bookings = booking_db.get_bookings_by_customer(customer_email)
        
        if not bookings:
            return f"No bookings found for {customer_email}."
        
        result = f"📋 Your Bookings ({len(bookings)} found):\n\n"
        
        for booking in bookings:
            status_emoji = "✅" if booking.status == BookingStatus.CONFIRMED else "⏳"
            result += f"""{status_emoji} {booking.booking_id}
• Product: {booking.product_name}
• Date: {booking.booking_date.strftime('%Y-%m-%d')}
• Time: {booking.time_slot}
• Status: {booking.status.value}

"""
        
        return result
        
    except Exception as e:
        return f"❌ Error retrieving bookings: {str(e)}"


def get_confirmed_bookings() -> str:
    """
    Get all confirmed bookings.
    
    Returns:
        List of all confirmed bookings
    """
    try:
        confirmed_bookings = booking_db.get_confirmed_bookings()
        
        if not confirmed_bookings:
            return "No confirmed bookings found."
        
        result = f"✅ Confirmed Bookings ({len(confirmed_bookings)} found):\n\n"
        
        for booking in confirmed_bookings:
            result += f"""📋 {booking.booking_id}
• Product: {booking.product_name}
• Customer: {booking.customer_name}
• Date: {booking.booking_date.strftime('%Y-%m-%d')}
• Time: {booking.time_slot}
• Email: {booking.customer_email}

"""
        
        return result
        
    except Exception as e:
        return f"❌ Error retrieving confirmed bookings: {str(e)}"
