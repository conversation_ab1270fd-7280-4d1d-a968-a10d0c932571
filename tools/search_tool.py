"""
Search database tool for products using Qdrant.
"""
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from sentence_transformers import SentenceTransformer
import uuid
from models.products import ProductDatabase, Product


class SearchTool:
    """Tool for searching products in the database using Qdrant vector search."""

    def __init__(self, qdrant_url: str = "http://localhost:6333", collection_name: str = "products"):
        self.product_db = ProductDatabase()
        self.qdrant_client = QdrantClient(url=qdrant_url)
        self.collection_name = collection_name
        self.encoder = SentenceTransformer('all-MiniLM-L6-v2')
        self._initialize_collection()

    def _initialize_collection(self):
        """Initialize Qdrant collection with product data."""
        try:
            # Check if collection exists
            collections = self.qdrant_client.get_collections()
            collection_exists = any(col.name == self.collection_name for col in collections.collections)

            if not collection_exists:
                # Create collection
                self.qdrant_client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(size=384, distance=Distance.COSINE)
                )

                # Add products to collection
                self._index_products()
        except Exception as e:
            print(f"Warning: Could not initialize Qdrant collection: {e}")

    def _index_products(self):
        """Index all products in Qdrant."""
        products = self.product_db.get_all_products()
        points = []

        for i, product in enumerate(products):
            # Create searchable text
            search_text = f"{product.name} {product.description} {product.category} {' '.join(product.documents)}"

            # Generate embedding
            embedding = self.encoder.encode(search_text).tolist()

            # Create point
            point = PointStruct(
                id=i,
                vector=embedding,
                payload=product.to_dict()
            )
            points.append(point)

        # Upload points to Qdrant
        self.qdrant_client.upsert(
            collection_name=self.collection_name,
            points=points
        )

    def search_products(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search products using vector similarity.

        Args:
            query: Search query
            limit: Maximum number of results

        Returns:
            List of matching products with similarity scores
        """
        try:
            # Generate query embedding
            query_embedding = self.encoder.encode(query).tolist()

            # Search in Qdrant
            search_results = self.qdrant_client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=limit
            )

            # Format results
            results = []
            for result in search_results:
                product_data = result.payload
                product_data['similarity_score'] = result.score
                results.append(product_data)

            return results

        except Exception as e:
            print(f"Error searching products: {e}")
            # Fallback to simple text search
            return self._fallback_search(query, limit)
    
    def get_product_details(self, product_name: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific product.
        
        Args:
            product_name: Name of the product
            
        Returns:
            Product details or None if not found
        """
        product = self.product_db.get_product_by_name(product_name)
        return product.to_dict() if product else None
    
    def get_categories(self) -> List[str]:
        """
        Get all available product categories.
        
        Returns:
            List of unique categories
        """
        products = self.product_db.get_all_products()
        categories = list(set(product.category for product in products))
        return sorted(categories)
    
    def get_price_range(self) -> Dict[str, int]:
        """
        Get the price range of all products.
        
        Returns:
            Dictionary with min and max prices
        """
        products = self.product_db.get_all_products()
        if not products:
            return {"min_price": 0, "max_price": 0}
        
        prices = [product.price for product in products]
        return {
            "min_price": min(prices),
            "max_price": max(prices)
        }
    
    def search_by_documents(self, required_document: str) -> List[Dict[str, Any]]:
        """
        Search products that require a specific document.
        
        Args:
            required_document: Document name to search for
            
        Returns:
            List of products that require the document
        """
        products = self.product_db.get_all_products()
        results = []
        
        for product in products:
            if any(required_document.lower() in doc.lower() for doc in product.documents):
                results.append(product.to_dict())
        
        return results
    
    def search_by_time_slot(self, time_preference: str) -> List[Dict[str, Any]]:
        """
        Search products by time slot preference.
        
        Args:
            time_preference: Time preference (morning, afternoon, evening)
            
        Returns:
            List of products with matching time slots
        """
        products = self.product_db.get_all_products()
        results = []
        
        time_mapping = {
            "morning": ["6:00 AM", "7:00 AM", "8:00 AM", "9:00 AM", "10:00 AM"],
            "afternoon": ["12:00 PM", "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM", "5:00 PM"],
            "evening": ["6:00 PM", "7:00 PM", "8:00 PM", "9:00 PM"]
        }
        
        target_times = time_mapping.get(time_preference.lower(), [])
        
        for product in products:
            for slot in product.time_slots:
                if any(time in slot for time in target_times):
                    results.append(product.to_dict())
                    break
        
        return results
    
    def get_recommendations(self, budget: int, category_preference: str = "") -> List[Dict[str, Any]]:
        """
        Get product recommendations based on budget and preferences.
        
        Args:
            budget: Maximum budget
            category_preference: Preferred category
            
        Returns:
            List of recommended products
        """
        products = self.search_products(
            category=category_preference,
            max_price=budget,
            available_only=True
        )
        
        # Sort by price (ascending) and available slots (descending)
        products.sort(key=lambda x: (x["price"], -x["available_slots"]))
        
        return products[:5]  # Return top 5 recommendations
