"""
Search database tool using Qdrant vector search.
"""
import os
from langchain_openai import OpenAIEmbeddings
from qdrant_client import QdrantClient
from langchain_qdrant import QdrantVectorStore,RetrievalMode

from dotenv import load_dotenv
load_dotenv()
embeddings = OpenAIEmbeddings(
    model="text-embedding-3-large",
    api_key=os.getenv("OPENAI_API_KEY"),
    dimensions=1536
)

# Initialize Qdrant client
qdrant_client = QdrantClient(
    host="*************",
    port=6333,
)

vector_store = QdrantVectorStore(
    client=qdrant_client,
    collection_name="langsmit_test",
    embedding=embeddings,
    retrieval_mode=RetrievalMode.DENSE,
    content_payload_key="page_content",
    metadata_payload_key="metadata"
)


def search_database(query: str, category: str = "all") -> str:
    """
    Search the database for products or services using Qdrant vector search.

    Args:
        query: The search term to look for
        category: Filter by category (optional, not used in current implementation)

    Returns:
        A formatted string with search results
    """
    try:
        # Setup embeddings for Qdrant

        # Search without filter for now to avoid validation issues
        docs = vector_store.similarity_search(query, k=5)

        if not docs:
            return f"No results found for '{query}'"

        results = []
        for doc in docs:
            # Format the result nicely
            content = doc.page_content
            source = doc.metadata.get('source', 'Unknown')
            results.append(f"📄 {source}: {content}")

        return "🔍 Search Results:\n" + "\n".join(results)

    except Exception as e:
        return f"Error searching database: {str(e)}"