"""
Search database tool for products.
"""
from typing import List, Dict, Any, Optional
from models.products import ProductDatabase, Product


class SearchTool:
    """Tool for searching products in the database."""
    
    def __init__(self):
        self.product_db = ProductDatabase()
    
    def search_products(self, query: str = "", category: str = "", 
                       min_price: int = 0, max_price: int = 999999,
                       available_only: bool = True) -> List[Dict[str, Any]]:
        """
        Search products based on various criteria.
        
        Args:
            query: Search query for product name or description
            category: Filter by category
            min_price: Minimum price filter
            max_price: Maximum price filter
            available_only: Only show products with available slots
            
        Returns:
            List of matching products as dictionaries
        """
        products = self.product_db.get_all_products()
        results = []
        
        for product in products:
            # Filter by availability
            if available_only and product.available_slots <= 0:
                continue
            
            # Filter by price range
            if not (min_price <= product.price <= max_price):
                continue
            
            # Filter by category
            if category and product.category != category:
                continue
            
            # Filter by query (name or description)
            if query:
                query_lower = query.lower()
                if (query_lower not in product.name.lower() and 
                    query_lower not in product.description.lower()):
                    continue
            
            results.append(product.to_dict())
        
        return results
    
    def get_product_details(self, product_name: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific product.
        
        Args:
            product_name: Name of the product
            
        Returns:
            Product details or None if not found
        """
        product = self.product_db.get_product_by_name(product_name)
        return product.to_dict() if product else None
    
    def get_categories(self) -> List[str]:
        """
        Get all available product categories.
        
        Returns:
            List of unique categories
        """
        products = self.product_db.get_all_products()
        categories = list(set(product.category for product in products))
        return sorted(categories)
    
    def get_price_range(self) -> Dict[str, int]:
        """
        Get the price range of all products.
        
        Returns:
            Dictionary with min and max prices
        """
        products = self.product_db.get_all_products()
        if not products:
            return {"min_price": 0, "max_price": 0}
        
        prices = [product.price for product in products]
        return {
            "min_price": min(prices),
            "max_price": max(prices)
        }
    
    def search_by_documents(self, required_document: str) -> List[Dict[str, Any]]:
        """
        Search products that require a specific document.
        
        Args:
            required_document: Document name to search for
            
        Returns:
            List of products that require the document
        """
        products = self.product_db.get_all_products()
        results = []
        
        for product in products:
            if any(required_document.lower() in doc.lower() for doc in product.documents):
                results.append(product.to_dict())
        
        return results
    
    def search_by_time_slot(self, time_preference: str) -> List[Dict[str, Any]]:
        """
        Search products by time slot preference.
        
        Args:
            time_preference: Time preference (morning, afternoon, evening)
            
        Returns:
            List of products with matching time slots
        """
        products = self.product_db.get_all_products()
        results = []
        
        time_mapping = {
            "morning": ["6:00 AM", "7:00 AM", "8:00 AM", "9:00 AM", "10:00 AM"],
            "afternoon": ["12:00 PM", "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM", "5:00 PM"],
            "evening": ["6:00 PM", "7:00 PM", "8:00 PM", "9:00 PM"]
        }
        
        target_times = time_mapping.get(time_preference.lower(), [])
        
        for product in products:
            for slot in product.time_slots:
                if any(time in slot for time in target_times):
                    results.append(product.to_dict())
                    break
        
        return results
    
    def get_recommendations(self, budget: int, category_preference: str = "") -> List[Dict[str, Any]]:
        """
        Get product recommendations based on budget and preferences.
        
        Args:
            budget: Maximum budget
            category_preference: Preferred category
            
        Returns:
            List of recommended products
        """
        products = self.search_products(
            category=category_preference,
            max_price=budget,
            available_only=True
        )
        
        # Sort by price (ascending) and available slots (descending)
        products.sort(key=lambda x: (x["price"], -x["available_slots"]))
        
        return products[:5]  # Return top 5 recommendations
