"""
Clean agentic chat system with booking tools.
"""
import os
from typing import Annotated
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, MessagesState
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from dotenv import load_dotenv

# Import our tools
from tools.search_tool import search_database
from tools.booking_tool import (
    book_appointment, 
    get_booking_by_id, 
    confirm_booking, 
    get_my_bookings, 
    get_confirmed_bookings
)
from tools.date_tool import get_current_date

# Load environment variables
load_dotenv()

# Convert functions to LangChain tools
@tool
def search_database_tool(query: str, category: str = "all") -> str:
    """Search the database for products using Qdrant vector search."""
    return search_database(query, category)

@tool
def book_appointment_tool(product_name: str, customer_name: str, customer_email: str, 
                         customer_phone: str, time_slot: str, booking_date: str) -> str:
    """Book an appointment for a product with time slot management."""
    return book_appointment(product_name, customer_name, customer_email, 
                          customer_phone, time_slot, booking_date)

@tool
def get_booking_tool(booking_id: str) -> str:
    """Get booking details by booking ID."""
    return get_booking_by_id(booking_id)

@tool
def confirm_booking_tool(booking_id: str) -> str:
    """Confirm a booking by changing its status."""
    return confirm_booking(booking_id)

@tool
def get_customer_bookings_tool(customer_email: str) -> str:
    """Get all bookings for a customer by email."""
    return get_my_bookings(customer_email)

@tool
def get_confirmed_bookings_tool() -> str:
    """Get all confirmed bookings."""
    return get_confirmed_bookings()

@tool
def get_current_date_tool() -> str:
    """Get current date information."""
    return get_current_date()

# List of all tools
tools = [
    search_database_tool,
    book_appointment_tool,
    get_booking_tool,
    confirm_booking_tool,
    get_customer_bookings_tool,
    get_confirmed_bookings_tool,
    get_current_date_tool
]

# Initialize the language model
llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    google_api_key=os.getenv("GOOGLE_API_KEY"),
    temperature=0.1
)

# Bind tools to the model
llm_with_tools = llm.bind_tools(tools)

# System prompt
SYSTEM_PROMPT = """You are a helpful booking assistant. You can:

🔍 **Search Database**: Search for products and courses using Qdrant vector search
📅 **Book Appointments**: Create bookings with time slots and manage student quotas  
📋 **Manage Bookings**: Get booking details, confirm bookings, and track status
📅 **Date Information**: Get current date when needed

**Available Products:**
- SEE (Rs. 1499) - Secondary Education Exam prep
- Bridge Course (Rs. 1999) - Higher secondary prep
- BBS (Rs. 2499) - Bachelor of Business Studies
- BBS Finance (Rs. 2699) - BBS with Finance specialization
- CSIT (Rs. 2999) - Computer Science program
- IELTS (Rs. 4999) - English language test prep
- GRE (Rs. 6999) - Graduate Record Exam prep
- TOEFL (Rs. 4599) - English proficiency test
- SAT (Rs. 5999) - Scholastic Assessment Test
- GMAT (Rs. 7499) - Graduate Management Admission Test

**Booking Process:**
1. Search for products first if customer needs information
2. Collect: product name, customer details (name, email, phone), preferred time slot, date
3. Create booking (generates unique booking ID)
4. Confirm booking if requested

Be helpful, friendly, and guide users through the process step by step.
"""

def should_continue(state: MessagesState):
    """Decide whether to continue or end the conversation."""
    messages = state['messages']
    last_message = messages[-1]
    
    # If the last message has tool calls, continue to tools
    if last_message.tool_calls:
        return "tools"
    # Otherwise, end
    return "end"

def call_model(state: MessagesState):
    """Call the language model with tools."""
    messages = state['messages']
    
    # Add system prompt if this is the first message
    if len(messages) == 1 or not any(isinstance(msg, AIMessage) for msg in messages):
        system_message = HumanMessage(content=SYSTEM_PROMPT)
        messages = [system_message] + messages
    
    response = llm_with_tools.invoke(messages)
    return {"messages": [response]}

# Create the graph
workflow = StateGraph(MessagesState)

# Add nodes
workflow.add_node("agent", call_model)
workflow.add_node("tools", ToolNode(tools))

# Set entry point
workflow.set_entry_point("agent")

# Add conditional edges
workflow.add_conditional_edges(
    "agent",
    should_continue,
    {"tools": "tools", "end": "__end__"}
)

# Add edge from tools back to agent
workflow.add_edge("tools", "agent")

# Compile the graph with memory
memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

class BookingAgent:
    """Main booking agent class."""
    
    def __init__(self):
        self.app = app
        self.thread_id = "default_thread"
    
    def chat(self, message: str) -> str:
        """Send a message to the agent and get response."""
        try:
            config = {"configurable": {"thread_id": self.thread_id}}
            
            # Invoke the agent
            result = self.app.invoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            # Get the last AI message
            last_message = result["messages"][-1]
            return last_message.content
            
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def new_conversation(self):
        """Start a new conversation thread."""
        import uuid
        self.thread_id = str(uuid.uuid4())
        print(f"🆕 Started new conversation: {self.thread_id}")

def main():
    """Main interactive chat function."""
    print("🤖 Booking Assistant Started!")
    print("Type 'quit' to exit, 'new' for new conversation")
    print("-" * 50)
    
    agent = BookingAgent()
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() in ['new', 'reset']:
                agent.new_conversation()
                continue
            elif not user_input:
                continue
            
            # Get response from agent
            response = agent.chat(user_input)
            print(f"\n🤖 Assistant: {response}")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
