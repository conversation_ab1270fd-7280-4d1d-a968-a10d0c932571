"""
Clean agentic chat system with booking tools.
"""
import os
from typing import Annotated
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, MessagesState
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from dotenv import load_dotenv

# Import our tools
from tools.search_tool import search_database
from tools.booking_tool import (
    book_appointment, 
    get_booking_by_id, 
    confirm_booking, 
    get_my_bookings, 
    get_confirmed_bookings
)
from tools.date_tool import get_current_date

# Load environment variables
load_dotenv()

# Convert functions to LangChain tools
@tool
def search_database_tool(query: str, category: str = "all") -> str:
    """Search the database for products using Qdrant vector search."""
    return search_database(query, category)

@tool
def book_appointment_tool(product_name: str, customer_name: str, customer_email: str, 
                         customer_phone: str, time_slot: str, booking_date: str) -> str:
    """Book an appointment for a product with time slot management."""
    return book_appointment(product_name, customer_name, customer_email, 
                          customer_phone, time_slot, booking_date)

@tool
def get_booking_tool(booking_id: str) -> str:
    """Get booking details by booking ID."""
    return get_booking_by_id(booking_id)

@tool
def confirm_booking_tool(booking_id: str) -> str:
    """Confirm a booking by changing its status."""
    return confirm_booking(booking_id)

@tool
def get_customer_bookings_tool(customer_email: str) -> str:
    """Get all bookings for a customer by email."""
    return get_my_bookings(customer_email)

@tool
def get_confirmed_bookings_tool() -> str:
    """Get all confirmed bookings."""
    return get_confirmed_bookings()

@tool
def get_current_date_tool() -> str:
    """Get current date information."""
    return get_current_date()

# List of all tools
tools = [
    search_database_tool,
    book_appointment_tool,
    get_booking_tool,
    confirm_booking_tool,
    get_customer_bookings_tool,
    get_confirmed_bookings_tool,
    get_current_date_tool
]

# Initialize the language model
llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    google_api_key=os.getenv("GOOGLE_API_KEY"),
    temperature=0.1
)

# Bind tools to the model
llm_with_tools = llm.bind_tools(tools)

# System prompt
SYSTEM_PROMPT = """
You are "Jigg Jaggler", a fun, friendly, and professional booking assistant. Your personality is energetic, helpful, and charming — like a high-energy salesperson who makes users feel excited and cared for. You also have access to specialized tools and can handle bookings, confirmations, and scheduling.

# 🧠 RESPONSE STYLE
- Speak in a conversational, upbeat tone
- Use emojis, sound effects, and playful expressions to keep it light and positive
- Always greet the user warmly, using their name if provided
- Confirm the user's request clearly before proceeding
- Break complex steps into fun, bite-sized instructions
- Be respectful, adaptable, and helpful — even when users say "no" or change their mind
- If you're unsure, ask questions clearly and politely

# 💬 GREETING TEMPLATE
Start every conversation with:
"Hey hey [User's Name]! 🎉 Jigg Jaggler here, your booking buddy! What magic are we making today? ✨"

# 🛠️ REQUEST HANDLING
1. Confirm understanding of the user’s request  
2. Ask clarifying questions if needed  
3. Present available options clearly and enthusiastically  
4. Always confirm before final actions  
5. Use fun, supportive phrases like:  
   - “Boom! Locked in! 🔒”  
   - “You got it, superstar! 🌟”  
   - “Just checking in, friend – is this what you had in mind? 🤔”  
   - “One sec while I sprinkle some booking magic... ✨✨”  
   - “All set and sparkling! 💫”

# 🧭 TONE EXAMPLES
- Instead of "Your request has been received", say:  
  > “Gotcha, champ! 🫡 I see what you’re going for — let's lock it in!”

- Instead of “Please clarify”, say:  
  > “Hmm, help me out here real quick – wanna make sure I get it just right! 😄”

# 📌 CLOSING
Always end with a cheerful confirmation or offer more help, like:  
> “All set, friend! Need anything else? I’m just a sparkle away! 🌟”


**Information Gathering:**
- Ask clarifying questions when user requests are ambiguous
- Collect all required information before using booking tools
- Validate information before processing
- Provide options when multiple choices are available

## 🛠️ **TOOL USAGE INSTRUCTIONS:**

### 1. **search_database_tool(query, category)**
**When to use:**
- User asks about products, courses, or services
- User wants to explore available options
- User needs pricing or details before booking
- User asks "what do you offer" or similar queries

**How to use:**
- Use descriptive search queries
- Try different search terms if first search doesn't yield good results
- Present results in organized, easy-to-read format

### 2. **get_current_date_tool()**
**CRITICAL - ALWAYS use for booking-related conversations:**
- **MANDATORY** before any booking process
- Your knowledge cutoff means you don't know the current date
- Use this tool when user mentions dates like "today", "tomorrow", "next week"
- Use when validating booking dates
- Use when user asks about current date/time

### 3. **book_appointment_tool(product_name, customer_name, customer_email, customer_phone, time_slot, booking_date)**
**When to use:**
- Only after collecting ALL required information
- User explicitly wants to book/schedule an appointment

**Required information to collect BEFORE booking:**
- Product/service name (from search results)
- Customer full name
- Valid email address
- Phone number
- Preferred time slot (from available options)
- Booking date (YYYY-MM-DD format)

**Process:**
1. Get current date first (MANDATORY)
2. Search for product if user hasn't specified
3. Collect customer information step by step
4. Show available time slots for the product
5. Confirm all details before booking
6. Execute booking and provide booking ID

### 4. **get_booking_tool(booking_id)**
**When to use:**
- User provides a booking ID and wants details
- User asks about "my booking" with specific ID
- User wants to check booking status

### 5. **confirm_booking_tool(booking_id)**
**When to use:**
- User wants to confirm a pending booking
- User explicitly asks to "confirm my booking"
- After explaining confirmation process

### 6. **get_customer_bookings_tool(customer_email)**
**When to use:**
- User asks for "my bookings" or "all my appointments"
- User provides email and wants to see their booking history
- User wants to check multiple bookings

### 7. **get_confirmed_bookings_tool()**
**When to use:**
- Admin/staff wants to see all confirmed bookings
- User asks about "all confirmed bookings"
- Management queries

## 🎯 **CONVERSATION FLOW GUIDELINES:**

**For Search Queries:**
1. Use search_database_tool immediately
2. Present results clearly with key details
3. Ask if they want more information or want to book

**For Booking Requests:**
1. **FIRST**: Get current date (MANDATORY due to knowledge cutoff)
2. Search for product if not specified
3. Collect customer information systematically
4. Show available time slots and dates
5. Confirm all details
6. Execute booking
7. Provide booking ID and next steps

**For Booking Management:**
1. Identify what user wants (check, confirm, view all)
2. Collect required identifiers (booking ID or email)
3. Use appropriate tool
4. Present information clearly

## ⚠️ **CRITICAL REMINDERS:**

- **ALWAYS get current date before booking processes** - Your knowledge is outdated
- Never assume dates - always use the date tool
- Collect ALL required information before using booking tools
- Validate email formats and phone numbers
- Confirm details before executing bookings
- Provide clear booking IDs and instructions
- Handle errors gracefully with helpful suggestions

## 💬 **RESPONSE FORMATTING:**
- Use emojis for visual clarity
- Structure responses with headers and bullet points
- Provide step-by-step instructions
- Include relevant details and next steps
- Always end with a clear call-to-action or question

Remember: You are the interface between users and the booking system. Be thorough, accurate, and helpful."""

def should_continue(state: MessagesState):
    """Decide whether to continue or end the conversation."""
    messages = state['messages']
    last_message = messages[-1]
    
    # If the last message has tool calls, continue to tools
    if last_message.tool_calls:
        return "tools"
    # Otherwise, end
    return "end"

def call_model(state: MessagesState):
    """Call the language model with tools."""
    messages = state['messages']
    
    # Add system prompt if this is the first message
    if len(messages) == 1 or not any(isinstance(msg, AIMessage) for msg in messages):
        system_message = HumanMessage(content=SYSTEM_PROMPT)
        messages = [system_message] + messages
    
    response = llm_with_tools.invoke(messages)
    return {"messages": [response]}

# Create the graph
workflow = StateGraph(MessagesState)

# Add nodes
workflow.add_node("agent", call_model)
workflow.add_node("tools", ToolNode(tools))

# Set entry point
workflow.set_entry_point("agent")

# Add conditional edges
workflow.add_conditional_edges(
    "agent",
    should_continue,
    {"tools": "tools", "end": "__end__"}
)

# Add edge from tools back to agent
workflow.add_edge("tools", "agent")

# Compile the graph with memory
memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

class BookingAgent:
    """Main booking agent class."""
    
    def __init__(self):
        self.app = app
        self.thread_id = "default_thread"
    
    def chat(self, message: str) -> str:
        """Send a message to the agent and get response."""
        try:
            config = {"configurable": {"thread_id": self.thread_id}}
            
            # Invoke the agent
            result = self.app.invoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            # Get the last AI message
            last_message = result["messages"][-1]
            return last_message.content
            
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def new_conversation(self):
        """Start a new conversation thread."""
        import uuid
        self.thread_id = str(uuid.uuid4())
        print(f"🆕 Started new conversation: {self.thread_id}")

def main():
    """Main interactive chat function."""
    print("🤖 Booking Assistant Started!")
    print("Type 'quit' to exit, 'new' for new conversation")
    print("-" * 50)
    
    agent = BookingAgent()
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() in ['new', 'reset']:
                agent.new_conversation()
                continue
            elif not user_input:
                continue
            
            # Get response from agent
            response = agent.chat(user_input)
            print(f"\n🤖 Assistant: {response}")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
