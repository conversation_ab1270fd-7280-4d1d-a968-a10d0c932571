# Agentic Chat System with LangGraph

An intelligent customer service chatbot built with LangGraph and Google Gemini that can search databases and book appointments with memory management.

## Features

### 🔍 **Search Database Tool**
- Search for products and services
- Filter by categories (Electronics, Audio, Furniture, Support, Warranty)
- Returns detailed product information including prices and descriptions

### 📅 **Book Appointment Tool**
- Collect customer information (name, email, phone)
- Schedule appointments with date and time
- Validate appointment availability
- Generate confirmation with appointment ID

### 🧠 **Memory Management**
- Maintains conversation context across multiple interactions
- Each user session has persistent memory
- Can start new conversations while preserving history

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env and add your Google API key
   ```

3. **Get Google API Key**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file

## Usage

Run the chatbot:
```bash
python agentic_chat_system.py
```

Choose between:
1. **Interactive Chat** - Real-time conversation with the bot
2. **Demo Conversation** - See pre-programmed examples

## Example Conversations

### Search Database
```
User: I'm looking for a laptop
Bot: [Searches database and returns laptop products with prices]

User: Show me audio products
Bot: [Filters and shows audio category items]
```

### Book Appointment
```
User: I'd like to book an appointment
Bot: I'd be happy to help you book an appointment. Could you please provide:
     - Your full name
     - Email address
     - Phone number
     - Preferred date and time

User: My name is John Smith, email <EMAIL>, phone 555-0123
Bot: Thank you! What date and time would you prefer?

User: Tomorrow at 2PM for tech support
Bot: [Books appointment and provides confirmation with ID]
```

## Architecture

The system uses:
- **LangGraph**: For agentic behavior and tool orchestration
- **Google Gemini**: As the language model
- **LangChain Tools**: For structured function calling
- **Memory Saver**: For conversation persistence

## Tools Available

### `search_database(query, category)`
- **Purpose**: Search products and services
- **Parameters**:
  - `query`: Search term
  - `category`: Filter category (optional)
- **Returns**: Formatted search results

### `book_appointment(name, email, phone, date, time, service_type)`
- **Purpose**: Schedule customer appointments
- **Parameters**: All customer and appointment details
- **Returns**: Confirmation with appointment ID

## Memory Management

- Each conversation has a unique thread ID
- Conversation history is maintained throughout the session
- Users can start new conversations while keeping context
- Memory persists across tool calls and responses

## Customization

You can easily customize:
- **Database Content**: Modify `sample_database` in the code
- **Available Time Slots**: Adjust `appointment_slots` generation
- **System Prompt**: Update the assistant's behavior and personality
- **Tools**: Add new tools by creating functions with `@tool` decorator