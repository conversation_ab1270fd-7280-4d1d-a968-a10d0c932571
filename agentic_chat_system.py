import os
from dotenv import load_dotenv
import uuid
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

# Lang<PERSON>hain imports
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage

# LangGraph imports for agentic behavior and memory
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

# Qdrant imports
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore,RetrievalMode

# Setup Gemini model
model = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Setup embeddings for Qdrant
embeddings = OpenAIEmbeddings(
    model="text-embedding-3-large",
    api_key=os.getenv("OPENAI_API_KEY"),
    dimensions=1536
)

# Initialize Qdrant client (in-memory)
qdrant_client = QdrantClient(
    host="*************",
    port=6333,
)

vector_store=QdrantVectorStore(
    client=qdrant_client,
    collection_name="langsmit_test",
    embedding=embeddings,
    retrieval_mode=RetrievalMode.DENSE,
    content_payload_key="page_content",
    metadata_payload_key="metadata"
)

# Sample appointment slots
appointment_slots = []
base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
for i in range(14):  # Next 14 days
    date = base_date + timedelta(days=i)
    for hour in [9, 11, 14, 16]:  # Available slots: 9AM, 11AM, 2PM, 4PM
        appointment_slots.append({
            "date": date.replace(hour=hour).strftime("%Y-%m-%d"),
            "time": date.replace(hour=hour).strftime("%H:%M"),
            "available": True
        })

# Store booked appointments
booked_appointments = []

@tool
def search_database(query: str, category: str = "all") -> str:
    """
    Search the database for products or services using Qdrant vector search.

    Args:
        query: The search term to look for
        category: Filter by category (optional, not used in current implementation)

    Returns:
        A formatted string with search results
    """
    try:
        # Search without filter for now to avoid validation issues
        docs = vector_store.similarity_search(query, k=5)

        if not docs:
            return f"No results found for '{query}'"

        results = []
        for doc in docs:
            # Format the result nicely
            content = doc.page_content
            source = doc.metadata.get('source', 'Unknown')
            results.append(f"📄 {source}: {content}")

        return "🔍 Search Results:\n" + "\n".join(results)

    except Exception as e:
        return f"Error searching database: {str(e)}"



@tool
def book_appointment(name: str, email: str, phone: str, date: str, time: str, service_type: str = "General Consultation") -> str:
    """
    Book an appointment with customer details.
    
    Args:
        name: Customer's full name
        email: Customer's email address
        phone: Customer's phone number
        date: Preferred date in YYYY-MM-DD format
        time: Preferred time in HH:MM format
        service_type: Type of service needed
    
    Returns:
        Confirmation message with appointment details
    """
    # Validate inputs
    if not all([name, email, phone, date, time]):
        return "Error: All fields (name, email, phone, date, time) are required to book an appointment."
    
    # Check if the slot is available
    requested_slot = None
    for slot in appointment_slots:
        if slot["date"] == date and slot["time"] == time:
            requested_slot = slot
            break
    
    if not requested_slot:
        return f"Error: No appointment slot available for {date} at {time}. Please check available slots."
    
    if not requested_slot["available"]:
        return f"Error: The slot on {date} at {time} is already booked. Please choose another time."
    
    # Book the appointment
    appointment_id = f"APT-{len(booked_appointments) + 1:04d}"
    appointment = {
        "id": appointment_id,
        "name": name,
        "email": email,
        "phone": phone,
        "date": date,
        "time": time,
        "service_type": service_type,
        "status": "confirmed"
    }
    
    booked_appointments.append(appointment)
    requested_slot["available"] = False
    
    return f"""
Appointment Successfully Booked!

Appointment ID: {appointment_id}
Name: {name}
Email: {email}
Phone: {phone}
Date: {date}
Time: {time}
Service: {service_type}
Status: Confirmed

Please save your appointment ID for future reference.
A confirmation email will be sent to {email}.
"""

# Create system prompt for the agentic chatbot
system_prompt = """You are a professional AI assistant for a customer service center. You MUST use the available tools to help customers.

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both.

AVAILABLE TOOLS:
1. **search_database**: Use this tool for ANY question or queries about products, services, apps, or information
2. **book_appointment**: Use this tool to schedule appointments

CRITICAL RULES:
- ALWAYS use search_database tool for ANY question that asks for information, even general ones
- Use search_database for questions like "how to download app", "how to fix", "what is", etc.
- NEVER try to answer informational questions directly without searching first
- If someone asks about downloading, troubleshooting, products, or any "how to" questions, use search_database
- For appointment booking, use book_appointment tool
- Only provide general greetings without tools

EXAMPLES:
- User: "how to download the app" → Use search_database tool with query "app download"
- User: "How to download Ambition Guru app" → Use search_database tool with query "Ambition Guru app download"
- User: "I need a laptop" → Use search_database tool with query "laptop"
- User: "how to fix my phone" → Use search_database tool with query "phone fix troubleshooting"
- User: "Book appointment" → Use book_appointment tool
- User: "Hello" → Respond normally

IMPORTANT: You must use the search_database tool for any specific information requests. Do not rely on your own knowledge for product/service details.
"""

# Create list of available tools
tools = [search_database, book_appointment]

# Create memory for conversation persistence
checkpointer = MemorySaver()

# Create the agentic chatbot using LangGraph
agentic_chatbot = create_react_agent(
    model=model,
    tools=tools,
    prompt=system_prompt,
    checkpointer=checkpointer
)

def chat_interface():
    """Interactive chat interface for the agentic chatbot"""
    print("=" * 60)
    print("🤖 AGENTIC CUSTOMER SERVICE CHATBOT")
    print("=" * 60)
    print("Available services:")
    print("• Search for products and services")
    print("• Book appointments")
    print("• General customer support")
    print("\nType 'quit' to exit, 'new' to start a new conversation")
    print("=" * 60)
    
    # Create a unique thread for this conversation
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    while True:
        user_input = input("\n👤 You: ").strip()
        
        if user_input.lower() == 'quit':
            print("\n🤖 Thank you for using our service! Have a great day!")
            break
        elif user_input.lower() == 'new':
            config = {"configurable": {"thread_id": str(uuid.uuid4())}}
            print("\n🤖 Starting a new conversation...")
            continue
        elif not user_input:
            continue
        
        try:
            # Send message to the agentic chatbot
            user_message = {"messages": [HumanMessage(user_input)]}

            print("\n🤖 Assistant: ", end="", flush=True)

            # First try simple invoke
            response = agentic_chatbot.invoke(user_message, config=config)

            # Check if there were any tool calls in the conversation
            tool_used = False
            for message in response["messages"]:
                if hasattr(message, 'tool_calls') and message.tool_calls:
                    tool_used = True
                    print(f"🔧 Used tool: {message.tool_calls[0]['name']} ")

            # Print the final response
            final_message = response["messages"][-1]
            print(final_message.content)

            if not tool_used and any(keyword in user_input.lower() for keyword in ['app', 'download', 'product', 'service', 'search', 'find']):
                print("\n⚠️  Note: No tools were used. The agent should have used search_database for this query.")

        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            print("Please try again or contact support.")

def demo_conversation():
    """Run a demo conversation to show the chatbot capabilities"""
    print("\n" + "=" * 60)
    print("🎯 DEMO CONVERSATION")
    print("=" * 60)
    
    demo_inputs = [
        "Hello! I'm looking for a laptop",
        "Can you search for gaming products?",
        "I'd like to book an appointment for tech support",
        "My name is John Smith, <NAME_EMAIL>, phone is 555-0123",
        "I'd like to schedule for tomorrow at 2PM for technical support consultation"
    ]
    
    config = {"configurable": {"thread_id": str(uuid.uuid4())}}
    
    for user_input in demo_inputs:
        print(f"\n👤 User: {user_input}")
        
        try:
            user_message = {"messages": [HumanMessage(user_input)]}
            response = agentic_chatbot.invoke(user_message, config=config)
            print(f"🤖 Assistant: {response['messages'][-1].content}")
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def test_tools():
    """Test the tools directly"""
    print("\n🧪 Testing components...")

    # Test basic model
    print("\n1. Testing basic model:")
    try:
        basic_response = model.invoke("Hello, can you respond?")
        print(f"✅ Model works: {basic_response.content}")
    except Exception as e:
        print(f"❌ Model error: {e}")
        return

    # Test search tool
    print("\n2. Testing search_database tool:")
    try:
        result = search_database("app")
        print(f"✅ Search tool result: {result}")
    except Exception as e:
        print(f"❌ Search tool error: {e}")

    # Test if tools are properly bound to model
    print("\n3. Testing model with tools:")
    try:
        model_with_tools = model.bind_tools(tools)
        response = model_with_tools.invoke("Search for information about apps")
        print(f"✅ Model response: {response.content}")
        if hasattr(response, 'tool_calls') and response.tool_calls:
            print(f"✅ Tool calls detected: {response.tool_calls}")
        else:
            print("⚠️  No tool calls made")
    except Exception as e:
        print(f"❌ Model with tools error: {e}")

    # Test agent
    print("\n4. Testing agent:")
    try:
        config = {"configurable": {"thread_id": "test-thread"}}
        user_message = {"messages": [HumanMessage("Hello")]}
        response = agentic_chatbot.invoke(user_message, config=config)
        print(f"✅ Agent works: {response['messages'][-1].content}")
    except Exception as e:
        print(f"❌ Agent error: {e}")

if __name__ == "__main__":
    print("Choose an option:")
    print("1. Interactive Chat")
    print("2. Demo Conversation")
    print("3. Test Tools")

    choice = input("Enter your choice (1, 2, or 3): ").strip()

    if choice == "1":
        chat_interface()
    elif choice == "2":
        demo_conversation()
    elif choice == "3":
        test_tools()
    else:
        print("Invalid choice. Starting interactive chat...")
        chat_interface()
