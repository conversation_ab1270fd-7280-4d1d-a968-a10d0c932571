"""
Clean general AI assistant with tools.
"""
import os
from typing import Annotated
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.graph import StateGraph, MessagesState
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver
from dotenv import load_dotenv

# Import our tools
from tools.search_tool import search_database
from tools.booking_tool import (
    book_appointment, 
    get_booking_by_id, 
    confirm_booking, 
    get_my_bookings, 
    get_confirmed_bookings
)
from tools.date_tool import get_current_date

# Load environment variables
load_dotenv()

# Convert functions to LangChain tools
@tool
def search_database_tool(query: str, category: str = "all") -> str:
    """Search the database for products using Qdrant vector search."""
    return search_database(query, category)

@tool
def book_appointment_tool(product_name: str, customer_name: str, customer_email: str, 
                         customer_phone: str, time_slot: str, booking_date: str) -> str:
    """Book an appointment for a product with time slot management."""
    return book_appointment(product_name, customer_name, customer_email, 
                          customer_phone, time_slot, booking_date)

@tool
def get_booking_tool(booking_id: str) -> str:
    """Get booking details by booking ID."""
    return get_booking_by_id(booking_id)

@tool
def confirm_booking_tool(booking_id: str) -> str:
    """Confirm a booking by changing its status."""
    return confirm_booking(booking_id)

@tool
def get_customer_bookings_tool(customer_email: str) -> str:
    """Get all bookings for a customer by email."""
    return get_my_bookings(customer_email)

@tool
def get_confirmed_bookings_tool() -> str:
    """Get all confirmed bookings."""
    return get_confirmed_bookings()

@tool
def get_current_date_tool() -> str:
    """Get current date information."""
    return get_current_date()

# List of all tools
tools = [
    search_database_tool,
    book_appointment_tool,
    get_booking_tool,
    confirm_booking_tool,
    get_customer_bookings_tool,
    get_confirmed_bookings_tool,
    get_current_date_tool
]

# Initialize the language model
llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    google_api_key=os.getenv("GOOGLE_API_KEY"),
    temperature=0.1
)

# Bind tools to the model
llm_with_tools = llm.bind_tools(tools)

# System prompt
SYSTEM_PROMPT = """You are a helpful AI assistant with access to specialized tools. You can assist users with various tasks.

## 📋 **RESPONSE APPROACH:**
- Be professional, friendly, and helpful
- Use clear, conversational language
- Ask clarifying questions when requests are unclear
- Don't assume users only want booking services
- Help with any task within your capabilities

## 🛠️ **AVAILABLE TOOLS:**

### 1. **search_database_tool(query, category)**
**When to use:** User asks about products, courses, services, or wants to explore options
**How to use:** Use descriptive search queries, present results clearly

### 2. **get_current_date_tool()**
**IMPORTANT:** Always use this for any date-related conversations
**When to use:** 
- Before any booking process (MANDATORY - your knowledge is outdated)
- When user mentions dates like "today", "tomorrow"
- When validating booking dates

### 3. **book_appointment_tool(...)**
**When to use:** User wants to book/schedule an appointment
**Required info to collect FIRST:**
- Product/service name
- Customer full name, email, phone
- Preferred time slot and date
**Process:** Get current date → Search product → Collect info → Book

### 4. **get_booking_tool(booking_id)**
**When to use:** User provides booking ID and wants details

### 5. **confirm_booking_tool(booking_id)**
**When to use:** User wants to confirm a pending booking

### 6. **get_customer_bookings_tool(customer_email)**
**When to use:** User asks for their bookings/appointments

### 7. **get_confirmed_bookings_tool()**
**When to use:** Admin/staff wants to see all confirmed bookings

## 🎯 **CONVERSATION GUIDELINES:**
- Listen to what the user actually needs
- For searches: Use search tool and present results clearly
- For bookings: Get current date first, then collect all required info systematically
- For booking management: Use appropriate tool based on request
- Always confirm details before taking actions
- Provide clear next steps and helpful information

## ⚠️ **CRITICAL REMINDERS:**
- **ALWAYS get current date before booking processes** - Your knowledge is outdated
- Don't assume all requests are booking-related
- Be a general helpful assistant first
- Use tools only when appropriate for the user's request

Remember: You're a general AI assistant who happens to have booking capabilities, not just a booking system."""

def should_continue(state: MessagesState):
    """Decide whether to continue or end the conversation."""
    messages = state['messages']
    last_message = messages[-1]
    
    if last_message.tool_calls:
        return "tools"
    return "end"

def call_model(state: MessagesState):
    """Call the language model with tools."""
    messages = state['messages']
    
    # Add system prompt if this is the first message
    if len(messages) == 1 or not any(isinstance(msg, AIMessage) for msg in messages):
        system_message = HumanMessage(content=SYSTEM_PROMPT)
        messages = [system_message] + messages
    
    response = llm_with_tools.invoke(messages)
    return {"messages": [response]}

# Create the graph
workflow = StateGraph(MessagesState)
workflow.add_node("agent", call_model)
workflow.add_node("tools", ToolNode(tools))
workflow.set_entry_point("agent")
workflow.add_conditional_edges(
    "agent",
    should_continue,
    {"tools": "tools", "end": "__end__"}
)
workflow.add_edge("tools", "agent")

# Compile with memory
memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

class GeneralAssistant:
    """General AI assistant with tool capabilities."""
    
    def __init__(self):
        self.app = app
        self.thread_id = "default_thread"
    
    def chat(self, message: str) -> str:
        """Send a message to the assistant and get response."""
        try:
            config = {"configurable": {"thread_id": self.thread_id}}
            
            result = self.app.invoke(
                {"messages": [HumanMessage(content=message)]},
                config=config
            )
            
            last_message = result["messages"][-1]
            return last_message.content
            
        except Exception as e:
            return f"❌ Error: {str(e)}"
    
    def new_conversation(self):
        """Start a new conversation thread."""
        import uuid
        self.thread_id = str(uuid.uuid4())
        print(f"🆕 Started new conversation")

def main():
    """Main interactive chat function."""
    print("🤖 AI Assistant Ready!")
    print("I can help you with various tasks including searching and booking.")
    print("Type 'quit' to exit, 'new' for new conversation")
    print("-" * 50)
    
    assistant = GeneralAssistant()
    
    while True:
        try:
            user_input = input("\n👤 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() in ['new', 'reset']:
                assistant.new_conversation()
                continue
            elif not user_input:
                continue
            
            response = assistant.chat(user_input)
            print(f"\n🤖 Assistant: {response}")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
