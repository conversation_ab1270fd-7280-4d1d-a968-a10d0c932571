import getpass
import os

if not os.environ.get("GOOGLE_API_KEY"):
  os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter API key for Google Gemini: ")

from langchain.chat_models import init_chat_model

llm = init_chat_model("gemini-2.0-flash", model_provider="google_genai")


from langchain_openai import OpenAIEmbeddings

embeddings = OpenAIEmbeddings(model="text-embedding-3-large",api_key=os.environ["OPENAI_API_KEY"],dimensions=1536)

from langchain_core.output_parsers import PydanticToolsParser
from pydantic import BaseModel, Field


class add(BaseModel):
    """Add two integers."""

    a: int = Field(..., description="First integer")
    b: int = Field(..., description="Second integer")


class multiply(BaseModel):
    """Multiply two integers."""

    a: int = Field(..., description="First integer")
    b: int = Field(..., description="Second integer")




llm_with_tools = llm.bind_tools([add, multiply])

query = "What is 3 * 12?"

llm_with_tools.invoke(query)

from langchain_qdrant import QdrantVectorStore
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams

client = QdrantClient(
    host="*************",
    port=6333,
)



client.get_collection(collection_name="ag_salessupport_sentence_context")

scrolled = client.scroll(collection_name="ag_salessupport_sentence_context" ,limit=100)

# [Document(metadata={'source': 'news', '_id': '50d8d6ee-69bf-4173-a6a2-b254e9928965', '_collection_name': 'demo_collection'}, page_content='Robbers broke into the city bank and stole $1 million in cash.')]

import json

from langchain_core.documents import Document
docs=[]
for doc in scrolled[0]:

    node=doc.payload.get("_node_content",{})
    data=json.loads(node)
    print(data.get("text"))
    text=data.get("text")
    data.pop("text")
    docs.append(Document(page_content=text,metadata=data))


docs

from langchain_qdrant import QdrantVectorStore,RetrievalMode
qdrant = QdrantVectorStore(
    embedding=embeddings,
    collection_name="ag_salessupport_sentence_context",
    client=client,
    retrieval_mode=RetrievalMode.DENSE,
    content_payload_key="text",
    metadata_payload_key="payload"
    )


query = "hiu ko gget?"
retriever = qdrant.as_retriever(search_kwargs={"k": 10},search_type="mmr")
docs = retriever.invoke(query)
docs


memclient = QdrantClient(":memory:")

client.create_collection(
    collection_name="langsmit_test",
    vectors_config=VectorParams(size=1536, distance=Distance.COSINE),
)


vector_store = QdrantVectorStore(
    client=client,
    collection_name="langsmit_test",
    embedding=embeddings,
)

docs

vector_store.add_documents(documents=docs)

