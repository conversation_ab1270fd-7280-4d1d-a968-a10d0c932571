"""
Main booking system with integrated tools.
"""
from tools import (
    search_database,
    book_appointment,
    get_booking_by_id,
    confirm_booking,
    get_my_bookings,
    get_confirmed_bookings,
    get_current_date
)


class BookingSystem:
    """Main booking system class that integrates all tools."""
    
    def __init__(self):
        """Initialize the booking system."""
        print("🚀 Booking System Initialized!")
        print("Available tools:")
        print("1. search_database() - Search products using Qdrant")
        print("2. book_appointment() - Book appointments with time slots")
        print("3. get_booking_by_id() - Get booking details by ID")
        print("4. confirm_booking() - Confirm a booking")
        print("5. get_my_bookings() - Get customer bookings")
        print("6. get_confirmed_bookings() - Get all confirmed bookings")
        print("7. get_current_date() - Get current date")
    
    def search_products(self, query: str, category: str = "all") -> str:
        """Search for products using Qdrant vector search."""
        return search_database(query, category)
    
    def create_booking(self, product_name: str, customer_name: str, 
                      customer_email: str, customer_phone: str, 
                      time_slot: str, booking_date: str) -> str:
        """Create a new booking."""
        return book_appointment(
            product_name=product_name,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_phone=customer_phone,
            time_slot=time_slot,
            booking_date=booking_date
        )
    
    def get_booking_details(self, booking_id: str) -> str:
        """Get booking details by ID."""
        return get_booking_by_id(booking_id)
    
    def confirm_booking_status(self, booking_id: str) -> str:
        """Confirm a booking."""
        return confirm_booking(booking_id)
    
    def get_customer_bookings(self, customer_email: str) -> str:
        """Get all bookings for a customer."""
        return get_my_bookings(customer_email)
    
    def get_all_confirmed_bookings(self) -> str:
        """Get all confirmed bookings."""
        return get_confirmed_bookings()
    
    def get_today_date(self) -> str:
        """Get current date information."""
        return get_current_date()


# Example usage
if __name__ == "__main__":
    # Initialize the system
    system = BookingSystem()
    
    print("\n" + "="*50)
    print("EXAMPLE USAGE")
    print("="*50)
    
    # Get current date
    print("\n📅 Current Date:")
    print(system.get_today_date())
    
    # Search for products
    print("\n🔍 Searching for 'IELTS':")
    search_result = system.search_products("IELTS preparation course")
    print(search_result)
    
    # Create a booking
    print("\n📝 Creating a booking:")
    booking_result = system.create_booking(
        product_name="IELTS",
        customer_name="John Doe",
        customer_email="<EMAIL>",
        customer_phone="+977-9841234567",
        time_slot="7:00 AM - 9:00 AM",
        booking_date="2024-07-15"
    )
    print(booking_result)
    
    # Extract booking ID from result (simple parsing)
    if "Booking ID:" in booking_result:
        booking_id = booking_result.split("Booking ID: ")[1].split("\n")[0]
        
        # Get booking details
        print(f"\n📋 Getting booking details for {booking_id}:")
        details = system.get_booking_details(booking_id)
        print(details)
        
        # Confirm booking
        print(f"\n✅ Confirming booking {booking_id}:")
        confirmation = system.confirm_booking_status(booking_id)
        print(confirmation)
        
        # Get customer bookings
        print("\n📋 Getting customer bookings:")
        customer_bookings = system.get_customer_bookings("<EMAIL>")
        print(customer_bookings)
    
    print("\n" + "="*50)
    print("SYSTEM READY FOR USE!")
    print("="*50)
