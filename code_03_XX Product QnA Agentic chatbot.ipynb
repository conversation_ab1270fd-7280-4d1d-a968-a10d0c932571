{"cells": [{"cell_type": "markdown", "id": "ddf0b696-aa72-4094-81b1-9d9ca40547da", "metadata": {}, "source": ["## Setup Models"]}, {"cell_type": "code", "execution_count": 1, "id": "03f0f096-1d52-4d40-bcd2-6415a98adf99", "metadata": {}, "outputs": [], "source": ["from langchain_openai import AzureChatOpenAI\n", "from langchain_openai import AzureOpenAIEmbeddings\n", "import os\n", "\n", "#Setup the environment variables\n", "os.environ[\"AZURE_OPENAI_API_KEY\"]=\"********************************\"\n", "os.environ[\"AZURE_OPENAI_ENDPOINT\"]=\"https://agentic-ai-course-kumaran.openai.azure.com/\"\n", "\n", "#Setup the LLM\n", "model = AzureChatOpenAI(\n", "    azure_deployment=\"gpt-4o\" ,\n", "    api_version=\"2023-03-15-preview\",\n", "    model=\"gpt-4o\"\n", ")\n", "\n", "#Setup the Embedding\n", "embedding = AzureOpenAIEmbeddings(\n", "    model=\"text-embedding-3-large\",\n", "    openai_api_version=\"2023-05-15\"\n", ")"]}, {"cell_type": "markdown", "id": "af6e2f00-872c-4f6a-bdb2-8da1f1d319bc", "metadata": {}, "source": ["## 03.02. Add  Product Pricing function tool"]}, {"cell_type": "code", "execution_count": 2, "id": "cc54279d-156f-4c83-89fe-e571f8661eee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["            Name  Price  ShippingDays\n", "0  AlphaBook Pro   1499             2\n", "1     GammaAir X   1399             7\n", "2  SpectraBook S   2499             7\n", "3   OmegaPro G17   2199            14\n", "4  NanoEdge Flex   1699             2\n"]}], "source": ["import pandas as pd\n", "from langchain_core.tools import tool\n", "\n", "#Load the laptop product pricing CSV into a Pandas dataframe.\n", "product_pricing_df = pd.read_csv(\"data/Laptop pricing.csv\")\n", "print(product_pricing_df)\n", "\n", "@tool\n", "def get_laptop_price(laptop_name:str) -> int :\n", "    \"\"\"\n", "    This function returns the price of a laptop, given its name as input.\n", "    It performs a substring match between the input name and the laptop name.\n", "    If a match is found, it returns the pricxe of the laptop.\n", "    If there is NO match found, it returns -1\n", "    \"\"\"\n", "\n", "    #Filter Dataframe for matching names\n", "    match_records_df = product_pricing_df[\n", "                        product_pricing_df[\"Name\"].str.contains(\n", "                                                \"^\" + laptop_name, case=False)\n", "                        ]\n", "    #Check if a record was found, if not return -1\n", "    if len(match_records_df) == 0 :\n", "        return -1\n", "    else:\n", "        return match_records_df[\"Price\"].iloc[0]\n", "\n", "#Test the tool. Before running the test, comment the @tool annotation\n", "#print(get_laptop_price(\"alpha\"))\n", "#print(get_laptop_price(\"testing\"))\n", "    "]}, {"cell_type": "markdown", "id": "36d17eac", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "05d7d678-75e9-4897-83fa-b2ec108f96ed", "metadata": {}, "source": ["## 03.03. Add Product Features Retrieval Tool"]}, {"cell_type": "code", "execution_count": 3, "id": "b17cd9a7-1af7-4b89-8719-03af6c6b8d49", "metadata": {}, "outputs": [], "source": ["__import__('pysqlite3')\n", "import sys\n", "sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')\n", "\n", "from langchain.tools.retriever import create_retriever_tool\n", "from langchain_chroma import Chroma \n", "from langchain_community.document_loaders import PyPDFLoader\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "# Load, chunk and index the contents of the product featuers document.\n", "loader=PyPDFLoader(\"./data/Laptop product descriptions.pdf\")\n", "docs = loader.load()\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=1024, chunk_overlap=256)\n", "splits = text_splitter.split_documents(docs)\n", "\n", "#Create a vector store with Chroma\n", "prod_feature_store = Chroma.from_documents(\n", "    documents=splits, \n", "    embedding=embedding\n", ")\n", "\n", "get_product_features = create_retriever_tool(\n", "    prod_feature_store.as_retriever(search_kwargs={\"k\": 1}),\n", "    name=\"Get_Product_Features\",\n", "    description=\"\"\"\n", "    This store contains details about Laptops. It lists the available laptops\n", "    and their features including CPU, memory, storage, design and advantages\n", "    \"\"\"\n", ")\n", "\n", "#Test the product feature store\n", "#print(prod_feature_store.as_retriever().invoke(\"Tell me about the AlphaBook Pro\") )"]}, {"cell_type": "markdown", "id": "6b9cd2ff-6109-4558-a3af-d33e8d8d3470", "metadata": {}, "source": ["## 03.04.Setup a Product QnA chatbot"]}, {"cell_type": "code", "execution_count": 4, "id": "49623ad7-619b-4f5a-893e-d3999aac6b01", "metadata": {}, "outputs": [], "source": ["from langgraph.prebuilt import create_react_agent\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langchain_core.messages import AIMessage,HumanMessage,SystemMessage\n", "\n", "#Create a System prompt to provide a persona to the chatbot\n", "system_prompt = SystemMessage(\"\"\"\n", "    You are professional chatbot that answers questions about laptops sold by your company.\n", "    To answer questions about laptops, you will ONLY use the available tools and NOT your own memory.\n", "    You will handle small talk and greetings by producing professional responses.\n", "    \"\"\"\n", ")\n", "\n", "#Create a list of tools available\n", "tools = [get_laptop_price, get_product_features]\n", "\n", "#Create memory across questions in a conversation (conversation memory)\n", "checkpointer=MemorySaver()\n", "\n", "#Create a Product QnA Agent. This is actual a graph in langGraph\n", "product_QnA_agent=create_react_agent(\n", "                                model=model, #LLM to use\n", "                                tools=tools, #List of tools to use\n", "                                state_modifier=system_prompt, #The system prompt\n", "                                debug=False, #Debugging turned on if needed\n", "                                checkpointer=checkpointer #For conversation memory\n", ")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "351e5a12-0733-45f5-980c-01ee29914f39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "What are the features and pricing for GammaAir?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Get_Product_Features (call_0GuJg5VGR7Gj2U0lPyeE2RdE)\n", " Call ID: call_0GuJg5VGR7Gj2U0lPyeE2RdE\n", "  Args:\n", "    query: GammaAir\n", "  get_laptop_price (call_YuoqTJtKFwUwqNSga8lOAlwH)\n", " Call ID: call_YuoqTJtKFwUwqNSga8lOAlwH\n", "  Args:\n", "    laptop_name: GammaAir\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: get_laptop_price\n", "\n", "1399\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "The GammaAir X is equipped with the following features:\n", "\n", "- **Processor**: AMD Ryzen 7\n", "- **Memory**: 32GB of DDR4 RAM\n", "- **Storage**: 512GB NVMe SSD\n", "- **Design**: Thin and light form factor, perfect for portability\n", "\n", "The price for the GammaAir X is $1,399.\n"]}], "source": ["#Setup chatbot\n", "import uuid\n", "#To maintain memory, each request should be in the context of a thread.\n", "#Each user conversation will use a separate thread ID\n", "config = {\"configurable\": {\"thread_id\": uuid.uuid4()}}\n", "\n", "#Test the agent with an input\n", "inputs = {\"messages\":[\n", "                HumanMessage(\"What are the features and pricing for GammaAir?\")\n", "            ]}\n", "\n", "#Use streaming to print responses as the agent  does the work.\n", "#This is an alternate way to stream agent responses without waiting for the agent to finish\n", "for stream in product_QnA_agent.stream(inputs, config, stream_mode=\"values\"):\n", "    message=stream[\"messages\"][-1]\n", "    if isinstance(message, tuple):\n", "        print(message)\n", "    else:\n", "        message.pretty_print()"]}, {"cell_type": "markdown", "id": "4ae438bc-f7b2-42de-8b7b-9c5bbc6bd190", "metadata": {}, "source": ["## 03.05. Execute the Product QnA Chatbot"]}, {"cell_type": "code", "execution_count": 6, "id": "de0a5ffd-48ad-4e0b-a33c-c7cd1e89cdf5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----------------------------------------\n", "USER : Hello\n", "AGENT : Hello! How can I assist you today with our range of laptops?\n", "----------------------------------------\n", "USER : I am looking to buy a laptop\n", "AGENT : Great! I can help you with information about our laptops, including their features and pricing. Do you have any specific requirements or models in mind?\n", "----------------------------------------\n", "USER : Give me a list of available laptop names\n", "AGENT : Here is a list of available laptops:\n", "\n", "1. **AlphaBook Pro** - A sleek ultrabook with a 12th Gen Intel i7 processor, 16GB of DDR4 RAM, and a fast 1TB SSD. Ideal for professionals on the go.\n", "\n", "2. **GammaAir X** - Combines an AMD Ryzen 7 processor with 32GB of DDR4 memory and a 512GB NVMe SSD. Perfect for high performance in a portable design.\n", "\n", "3. **SpectraBook S** - Designed for power users, featuring an Intel Core i9 processor, 64GB RAM, and a massive 2TB SSD. Perfect for intensive tasks.\n", "\n", "4. **OmegaPro G17** - A gaming powerhouse with a Ryzen 9 5900HX CPU, 32GB RAM, and a 1TB SSD. Designed for gamers with a high refresh rate display.\n", "\n", "5. **<PERSON><PERSON><PERSON><PERSON> Flex** - (Details not provided)\n", "\n", "If you need more information or pricing for any of these models, feel free to ask!\n", "----------------------------------------\n", "USER : Tell me about the features of  SpectraBook\n", "AGENT : The **SpectraBook S** is designed for power users who require top-notch performance. Here are its key features:\n", "\n", "- **Processor:** Intel Core i9\n", "- **Memory:** 64GB RAM\n", "- **Storage:** Massive 2TB SSD \n", "\n", "This workstation-class laptop is perfect for intensive tasks such as video editing and 3D rendering. If you have any more questions or need further details, feel free to ask!\n", "----------------------------------------\n", "USER : How much does it cost?\n", "AGENT : The **SpectraBook S** is priced at $2,499. If you have any more questions or need assistance with anything else, feel free to ask!\n", "----------------------------------------\n", "USER : Give me similar information about OmegaPro\n", "AGENT : The **OmegaPro G17** is a gaming powerhouse designed specifically for gamers. Here are its key features:\n", "\n", "- **Processor:** Ryzen 9 5900HX CPU\n", "- **Memory:** 32GB RAM\n", "- **Storage:** 1TB SSD\n", "- **Display:** 17-inch with a high refresh rate\n", "- **Graphics:** Powerful graphics card for the best gaming experience\n", "\n", "The price of the OmegaPro G17 is $2,199. If you have any more questions or need further assistance, feel free to ask!\n", "----------------------------------------\n", "USER : What info do you have on AcmeRight ?\n", "AGENT : It appears that I don't have any information on a laptop named \"AcmeRight.\" If you have any other questions or need information on different models, please let me know!\n", "----------------------------------------\n", "USER : Thanks for the help\n", "AGENT : You're welcome! If you have any more questions or need assistance in the future, feel free to reach out. Have a great day!\n"]}], "source": ["import uuid\n", "#Send a sequence of messages to chatbot and get its response\n", "#This simulates the conversation between the user and the Agentic chatbot\n", "user_inputs = [\n", "    \"Hello\",\n", "    \"I am looking to buy a laptop\",\n", "    \"Give me a list of available laptop names\",\n", "    \"Tell me about the features of  SpectraBook\",\n", "    \"How much does it cost?\",\n", "    \"Give me similar information about Omega<PERSON><PERSON>\",\n", "    \"What info do you have on AcmeRight ?\",\n", "    \"Thanks for the help\"\n", "]\n", "\n", "#Create a new thread\n", "config = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "\n", "for input in user_inputs:\n", "    print(f\"----------------------------------------\\nUSER : {input}\")\n", "    #Format the user message\n", "    user_message = {\"messages\":[HumanMessage(input)]}\n", "    #Get response from the agent\n", "    ai_response = product_QnA_agent.invoke(user_message,config=config)\n", "    #Print the response\n", "    print(f\"AGENT : {ai_response['messages'][-1].content}\")\n", "    "]}, {"cell_type": "code", "execution_count": 7, "id": "211a1b46-a07b-4a6e-899f-d3bfdbb6344d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "USER 1: The SpectraBook S is designed for power users and features:\n", "\n", "- **Processor:** Intel Core i9\n", "- **Memory:** 64GB RAM\n", "- **Storage:** 2TB SSD\n", "\n", "This workstation-class laptop is perfect for intensive tasks like video editing and 3D rendering.\n", "\n", "USER 2: The GammaAir X laptop features an AMD Ryzen 7 processor, 32GB of DDR4 memory, and a 512GB NVMe SSD. It has a thin and light form factor, making it perfect for users who require high performance in a portable design.\n", "\n", "USER 1: The price of the SpectraBook S is $2,499.\n", "\n", "USER 2: The price of the GammaAir X laptop is $1399.\n"]}], "source": ["#conversation memory by user\n", "def execute_prompt(user, config, prompt):\n", "    inputs = {\"messages\":[(\"user\",prompt)]}\n", "    ai_response = product_QnA_agent.invoke(inputs,config=config)\n", "    print(f\"\\n{user}: {ai_response['messages'][-1].content}\")\n", "\n", "#Create different session threads for 2 users\n", "config_1 = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "config_2 = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "\n", "#Test both threads\n", "execute_prompt(\"USER 1\", config_1, \"Tell me about the features of  SpectraBook\")\n", "execute_prompt(\"USER 2\", config_2, \"Tell me about the features of  GammaAir\")\n", "execute_prompt(\"USER 1\", config_1, \"What is its price ?\")\n", "execute_prompt(\"USER 2\", config_2, \"What is its price ?\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "86c4b10e-c03d-45ed-834b-71ae5fec9717", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}